const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const AuthController = require('./controllers/authController');
const TaskController = require('./controllers/taskController');
const CategoryController = require('./controllers/categoryController');
const TagController = require('./controllers/tagController');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// JWT验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }
  
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) return res.status(403).json({ error: 'Invalid token' });
    req.user = user;
    next();
  });
};

// 认证路由
app.post('/api/auth/register', AuthController.register);
app.post('/api/auth/login', AuthController.login);
app.get('/api/auth/profile', authenticateToken, AuthController.getProfile);

// 任务路由
app.post('/api/tasks', authenticateToken, TaskController.createTask);
app.get('/api/tasks', authenticateToken, TaskController.getAllTasks);
app.get('/api/tasks/today', authenticateToken, TaskController.getTodayTasks);
app.post('/api/reviews/:review_id/complete', authenticateToken, TaskController.completeReview);

// 分类路由
app.get('/api/categories', authenticateToken, CategoryController.getCategories);
app.post('/api/categories', authenticateToken, CategoryController.createCategory);
app.put('/api/categories/:id', authenticateToken, CategoryController.updateCategory);
app.delete('/api/categories/:id', authenticateToken, CategoryController.deleteCategory);

// 标签路由
app.get('/api/tags', authenticateToken, TagController.getTags);
app.post('/api/tags', authenticateToken, TagController.createTag);
app.post('/api/tasks/tags', authenticateToken, TagController.addTagToTask);
app.delete('/api/tasks/:taskId/tags/:tagId', authenticateToken, TagController.removeTagFromTask);

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});