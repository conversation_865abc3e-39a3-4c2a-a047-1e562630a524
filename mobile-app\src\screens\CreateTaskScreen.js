import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  FlatList
} from 'react-native';
import ApiService from '../services/api';

const CreateTaskScreen = ({ navigation }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedTags, setSelectedTags] = useState([]);
  const [dailyTime, setDailyTime] = useState('30');
  const [loading, setLoading] = useState(false);
  
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');

  useEffect(() => {
    loadCategoriesAndTags();
  }, []);

  const loadCategoriesAndTags = async () => {
    try {
      const [categoriesRes, tagsRes] = await Promise.all([
        ApiService.getCategories(),
        ApiService.getTags()
      ]);
      setCategories(categoriesRes.data);
      setTags(tagsRes.data);
    } catch (error) {
      Alert.alert('错误', '加载分类和标签失败');
    }
  };

  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) return;
    
    try {
      await ApiService.createCategory({
        name: newCategoryName,
        color: '#2196F3',
        icon: 'book'
      });
      setNewCategoryName('');
      setShowNewCategoryInput(false);
      loadCategoriesAndTags();
    } catch (error) {
      Alert.alert('错误', '创建分类失败');
    }
  };

  const toggleTag = (tagId) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  const handleCreateTask = async () => {
    if (!title.trim()) {
      Alert.alert('错误', '请输入任务标题');
      return;
    }

    try {
      setLoading(true);
      const taskData = {
        title: title.trim(),
        description: description.trim(),
        category_id: selectedCategory,
        daily_time_minutes: parseInt(dailyTime) || 30,
        start_date: new Date().toISOString().split('T')[0],
        tags: selectedTags
      };

      await ApiService.createTask(taskData);
      Alert.alert('成功', '学习计划创建成功！', [
        { text: '确定', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert('错误', '创建失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const renderCategory = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.id && styles.selectedCategoryItem
      ]}
      onPress={() => setSelectedCategory(item.id)}
    >
      <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
      <Text style={styles.categoryName}>{item.name}</Text>
      <Text style={styles.taskCount}>({item.task_count})</Text>
    </TouchableOpacity>
  );

  const renderTag = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.tagItem,
        selectedTags.includes(item.id) && styles.selectedTagItem
      ]}
      onPress={() => toggleTag(item.id)}
    >
      <Text style={[
        styles.tagText,
        selectedTags.includes(item.id) && styles.selectedTagText
      ]}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.header}>创建学习计划</Text>
      
      <View style={styles.form}>
        <Text style={styles.label}>任务标题 *</Text>
        <TextInput
          style={styles.input}
          value={title}
          onChangeText={setTitle}
          placeholder="例如：背英语单词、学习React"
        />

        <Text style={styles.label}>任务描述</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={description}
          onChangeText={setDescription}
          placeholder="详细描述学习内容..."
          multiline
          numberOfLines={3}
        />

        <Text style={styles.label}>选择分类</Text>
        <FlatList
          data={categories}
          renderItem={renderCategory}
          keyExtractor={(item) => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryList}
        />
        
        <TouchableOpacity
          style={styles.addCategoryButton}
          onPress={() => setShowNewCategoryInput(!showNewCategoryInput)}
        >
          <Text style={styles.addCategoryText}>+ 新建分类</Text>
        </TouchableOpacity>
        
        {showNewCategoryInput && (
          <View style={styles.newCategoryContainer}>
            <TextInput
              style={styles.input}
              value={newCategoryName}
              onChangeText={setNewCategoryName}
              placeholder="分类名称"
            />
            <TouchableOpacity
              style={styles.createCategoryButton}
              onPress={handleCreateCategory}
            >
              <Text style={styles.createCategoryText}>创建</Text>
            </TouchableOpacity>
          </View>
        )}

        <Text style={styles.label}>选择标签</Text>
        <FlatList
          data={tags}
          renderItem={renderTag}
          keyExtractor={(item) => item.id.toString()}
          numColumns={3}
          style={styles.tagList}
        />

        <Text style={styles.label}>每日学习时间（分钟）</Text>
        <TextInput
          style={styles.input}
          value={dailyTime}
          onChangeText={setDailyTime}
          placeholder="30"
          keyboardType="numeric"
        />

        <TouchableOpacity
          style={[styles.createButton, loading && styles.disabledButton]}
          onPress={handleCreateTask}
          disabled={loading}
        >
          <Text style={styles.createButtonText}>
            {loading ? '创建中...' : '创建计划'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  form: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  categoryList: {
    marginBottom: 10,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    padding: 10,
    marginRight: 10,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedCategoryItem: {
    borderColor: '#2196F3',
    backgroundColor: '#e3f2fd',
  },
  categoryColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  categoryName: {
    fontSize: 14,
    marginRight: 4,
  },
  taskCount: {
    fontSize: 12,
    color: '#666',
  },
  addCategoryButton: {
    alignSelf: 'flex-start',
    marginBottom: 16,
  },
  addCategoryText: {
    color: '#2196F3',
    fontSize: 14,
  },
  newCategoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  createCategoryButton: {
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 6,
    marginLeft: 10,
  },
  createCategoryText: {
    color: 'white',
    fontSize: 14,
  },
  tagList: {
    marginBottom: 16,
  },
  tagItem: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    margin: 4,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedTagItem: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  tagText: {
    fontSize: 12,
    color: '#333',
  },
  selectedTagText: {
    color: 'white',
  },
  createButton: {
    backgroundColor: '#2196F3',
    padding: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 10,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CreateTaskScreen;