const db = require('../config/database');

class CategoryController {
  // 获取用户的所有分类
  static async getCategories(req, res) {
    try {
      const userId = req.user.id;
      
      const [categories] = await db.execute(`
        SELECT c.*, COUNT(lt.id) as task_count
        FROM categories c
        LEFT JOIN learning_tasks lt ON c.id = lt.category_id AND lt.status = 'active'
        WHERE c.user_id = ?
        GROUP BY c.id
        ORDER BY c.created_at ASC
      `, [userId]);
      
      res.json({ success: true, data: categories });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
  
  // 创建分类
  static async createCategory(req, res) {
    try {
      const { name, color, icon } = req.body;
      const userId = req.user.id;
      
      if (!name || !name.trim()) {
        return res.status(400).json({ 
          success: false, 
          error: 'Category name is required' 
        });
      }
      
      const [result] = await db.execute(
        'INSERT INTO categories (user_id, name, color, icon) VALUES (?, ?, ?, ?)',
        [userId, name.trim(), color || '#2196F3', icon || 'book']
      );
      
      res.status(201).json({
        success: true,
        data: { id: result.insertId, name, color, icon }
      });
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        res.status(400).json({ 
          success: false, 
          error: 'Category name already exists' 
        });
      } else {
        res.status(500).json({ success: false, error: error.message });
      }
    }
  }
  
  // 更新分类
  static async updateCategory(req, res) {
    try {
      const { id } = req.params;
      const { name, color, icon } = req.body;
      const userId = req.user.id;
      
      const [result] = await db.execute(
        'UPDATE categories SET name = ?, color = ?, icon = ? WHERE id = ? AND user_id = ?',
        [name, color, icon, id, userId]
      );
      
      if (result.affectedRows === 0) {
        return res.status(404).json({ 
          success: false, 
          error: 'Category not found' 
        });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
  
  // 删除分类
  static async deleteCategory(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      // 检查是否有任务使用此分类
      const [tasks] = await db.execute(
        'SELECT COUNT(*) as count FROM learning_tasks WHERE category_id = ?',
        [id]
      );
      
      if (tasks[0].count > 0) {
        return res.status(400).json({ 
          success: false, 
          error: 'Cannot delete category with existing tasks' 
        });
      }
      
      const [result] = await db.execute(
        'DELETE FROM categories WHERE id = ? AND user_id = ?',
        [id, userId]
      );
      
      if (result.affectedRows === 0) {
        return res.status(404).json({ 
          success: false, 
          error: 'Category not found' 
        });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
}

module.exports = CategoryController;