const db = require('../config/database');

class TagController {
  // 获取用户的所有标签
  static async getTags(req, res) {
    try {
      const userId = req.user.id;
      
      const [tags] = await db.execute(`
        SELECT t.*, COUNT(tt.learning_task_id) as usage_count
        FROM tags t
        LEFT JOIN task_tags tt ON t.id = tt.tag_id
        WHERE t.user_id = ?
        GROUP BY t.id
        ORDER BY usage_count DESC, t.created_at ASC
      `, [userId]);
      
      res.json({ success: true, data: tags });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
  
  // 创建标签
  static async createTag(req, res) {
    try {
      const { name, color } = req.body;
      const userId = req.user.id;
      
      if (!name || !name.trim()) {
        return res.status(400).json({ 
          success: false, 
          error: 'Tag name is required' 
        });
      }
      
      const [result] = await db.execute(
        'INSERT INTO tags (user_id, name, color) VALUES (?, ?, ?)',
        [userId, name.trim(), color || '#FF9800']
      );
      
      res.status(201).json({
        success: true,
        data: { id: result.insertId, name, color }
      });
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        res.status(400).json({ 
          success: false, 
          error: 'Tag name already exists' 
        });
      } else {
        res.status(500).json({ success: false, error: error.message });
      }
    }
  }
  
  // 为任务添加标签
  static async addTagToTask(req, res) {
    try {
      const { taskId, tagId } = req.body;
      const userId = req.user.id;
      
      // 验证任务属于当前用户
      const [tasks] = await db.execute(
        'SELECT id FROM learning_tasks WHERE id = ? AND user_id = ?',
        [taskId, userId]
      );
      
      if (tasks.length === 0) {
        return res.status(404).json({ 
          success: false, 
          error: 'Task not found' 
        });
      }
      
      // 验证标签属于当前用户
      const [tags] = await db.execute(
        'SELECT id FROM tags WHERE id = ? AND user_id = ?',
        [tagId, userId]
      );
      
      if (tags.length === 0) {
        return res.status(404).json({ 
          success: false, 
          error: 'Tag not found' 
        });
      }
      
      // 添加关联
      await db.execute(
        'INSERT IGNORE INTO task_tags (learning_task_id, tag_id) VALUES (?, ?)',
        [taskId, tagId]
      );
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
  
  // 从任务移除标签
  static async removeTagFromTask(req, res) {
    try {
      const { taskId, tagId } = req.params;
      const userId = req.user.id;
      
      // 验证任务属于当前用户
      const [tasks] = await db.execute(
        'SELECT id FROM learning_tasks WHERE id = ? AND user_id = ?',
        [taskId, userId]
      );
      
      if (tasks.length === 0) {
        return res.status(404).json({ 
          success: false, 
          error: 'Task not found' 
        });
      }
      
      await db.execute(
        'DELETE FROM task_tags WHERE learning_task_id = ? AND tag_id = ?',
        [taskId, tagId]
      );
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
}

module.exports = TagController;