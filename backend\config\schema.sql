-- 用户表
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 学习任务表
CREATE TABLE learning_tasks (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(50),
  daily_time_minutes INT DEFAULT 30,
  start_date DATE NOT NULL,
  status ENUM('active', 'paused', 'completed') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 复习任务表（基于艾宾浩斯曲线生成）
CREATE TABLE review_tasks (
  id INT PRIMARY KEY AUTO_INCREMENT,
  learning_task_id INT NOT NULL,
  review_round INT NOT NULL, -- 第几轮复习
  scheduled_date DATE NOT NULL,
  completed_at TIMESTAMP NULL,
  difficulty_rating INT DEFAULT 3, -- 1-5难度评分
  completion_quality ENUM('forgot', 'hard', 'good', 'easy') DEFAULT 'good',
  next_interval_days INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (learning_task_id) REFERENCES learning_tasks(id) ON DELETE CASCADE,
  INDEX idx_scheduled_date (scheduled_date),
  INDEX idx_learning_task (learning_task_id)
);