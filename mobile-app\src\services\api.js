import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = 'http://localhost:3001/api';

class ApiService {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
    });
    
    // 请求拦截器 - 添加token
    this.client.interceptors.request.use(async (config) => {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }
  
  // 认证相关
  async register(userData) {
    const response = await this.client.post('/auth/register', userData);
    return response.data;
  }
  
  async login(credentials) {
    const response = await this.client.post('/auth/login', credentials);
    return response.data;
  }
  
  async getProfile() {
    const response = await this.client.get('/auth/profile');
    return response.data;
  }
  
  // 任务相关
  async createTask(taskData) {
    const response = await this.client.post('/tasks', taskData);
    return response.data;
  }
  
  async getTodayTasks() {
    const response = await this.client.get('/tasks/today');
    return response.data;
  }
  
  async getAllTasks(filters = {}) {
    const params = new URLSearchParams(filters).toString();
    const response = await this.client.get(`/tasks?${params}`);
    return response.data;
  }
  
  async completeReview(reviewId, quality) {
    const response = await this.client.post(`/reviews/${reviewId}/complete`, {
      quality
    });
    return response.data;
  }
  
  // 分类相关
  async getCategories() {
    const response = await this.client.get('/categories');
    return response.data;
  }
  
  async createCategory(categoryData) {
    const response = await this.client.post('/categories', categoryData);
    return response.data;
  }
  
  async updateCategory(id, categoryData) {
    const response = await this.client.put(`/categories/${id}`, categoryData);
    return response.data;
  }
  
  async deleteCategory(id) {
    const response = await this.client.delete(`/categories/${id}`);
    return response.data;
  }
  
  // 标签相关
  async getTags() {
    const response = await this.client.get('/tags');
    return response.data;
  }
  
  async createTag(tagData) {
    const response = await this.client.post('/tags', tagData);
    return response.data;
  }
  
  async addTagToTask(taskId, tagId) {
    const response = await this.client.post('/tasks/tags', { taskId, tagId });
    return response.data;
  }
  
  async removeTagFromTask(taskId, tagId) {
    const response = await this.client.delete(`/tasks/${taskId}/tags/${tagId}`);
    return response.data;
  }
}

export default new ApiService();